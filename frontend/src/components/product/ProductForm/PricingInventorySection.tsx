import React, { useEffect } from 'react';
import { FiDollarSign, FiPackage, FiPercent, FiLayers, FiCheck } from 'react-icons/fi';

// Import components
import { NumberInput, ModalSelectInput } from '../../inputs';
import ProductVariantsSection from '../ProductVariantsSection';

// Import tax stores
import useTaxTypeStore from '../../../stores/taxTypeStore';
import useTaxRateStore from '../../../stores/taxRateStore';

interface PricingInventorySectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

const PricingInventorySection: React.FC<PricingInventorySectionProps> = ({
  formData,
  updateFormData,
  errors
}) => {
  // Tax stores
  const { taxTypes, loading: taxTypesLoading, fetchTaxTypes } = useTaxTypeStore();
  const { taxRates, loading: taxRatesLoading, fetchTaxRates } = useTaxRateStore();

  // Load tax data on component mount
  useEffect(() => {
    const loadTaxData = async () => {
      try {
        await fetchTaxTypes();
        await fetchTaxRates(); // Load all tax rates
      } catch (error) {
        console.error('خطأ في تحميل بيانات الضرائب:', error);
      }
    };

    loadTaxData();
  }, [fetchTaxTypes, fetchTaxRates]);

  // Product type options with enhanced info
  const productTypeOptions = [
    {
      value: 'simple',
      label: 'منتج فردي',
      description: 'منتج بسيط بسعر واحد ثابت',
      icon: FiPackage,
      color: 'blue'
    },
    {
      value: 'variable',
      label: 'منتج متعدد الخيارات',
      description: 'منتج بمتغيرات مختلفة (ألوان، أحجام، إلخ)',
      icon: FiLayers,
      color: 'green'
    }
  ];

  // Generate tax type options from tax types store
  const taxTypeOptions = taxTypes
    .filter(type => type.is_active)
    .map(type => ({
      value: type.id.toString(),
      label: type.name_ar || type.name
    }));

  // Generate tax rate options based on selected tax type
  const taxRateOptions = formData.tax_type_id 
    ? taxRates
        .filter(rate => 
          rate.is_active && 
          rate.tax_type_id === parseInt(formData.tax_type_id) &&
          rate.applies_to !== 'services' // Filter for products only
        )
        .map(rate => ({
          value: rate.id.toString(),
          label: `${rate.name} (${rate.rate_value}%)`
        }))
    : [];

  // Discount type options
  const discountTypeOptions = [
    { value: 'percentage', label: 'نسبة مئوية (%)' },
    { value: 'fixed', label: 'مبلغ ثابت' }
  ];

  // Calculate profit margin
  const calculateProfitMargin = () => {
    if (formData.cost_price > 0) {
      return (((formData.price - formData.cost_price) / formData.cost_price) * 100).toFixed(2);
    }
    return '0.00';
  };

  // Calculate tax amount
  const calculateTaxAmount = () => {
    if (formData.tax_rate_id && formData.price > 0) {
      const selectedTaxRate = taxRates.find(rate => rate.id === parseInt(formData.tax_rate_id));
      if (selectedTaxRate) {
        const baseAmount = formData.price - (formData.discount_type === 'fixed' ? formData.discount_value : formData.price * formData.discount_value / 100);
        return (baseAmount * selectedTaxRate.rate_value / 100).toFixed(2);
      }
    }
    return '0.00';
  };

  // Calculate total price with tax
  const calculateTotalPrice = () => {
    const netPrice = formData.price - (formData.discount_type === 'fixed' ? formData.discount_value : formData.price * formData.discount_value / 100);
    const taxAmount = parseFloat(calculateTaxAmount());
    
    // For now, we'll treat all taxes as exclusive (can be made configurable later)
    return (netPrice + taxAmount).toFixed(2);
  };



  return (
    <div className="space-y-8">

      {/* Product Type - Enhanced Card Design */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <FiPackage className="w-5 h-5 text-primary-600 dark:text-primary-400" />
          نوع المنتج
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {productTypeOptions.map((option) => {
            const isSelected = formData.product_type === option.value;
            const IconComponent = option.icon;
            const colorClasses = {
              blue: {
                border: isSelected ? 'border-blue-500 dark:border-blue-400' : 'border-gray-300 dark:border-gray-600',
                bg: isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-white dark:bg-gray-800',
                icon: 'text-blue-600 dark:text-blue-400',
                check: 'text-blue-600 dark:text-blue-400',
                title: isSelected ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white',
                desc: isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400'
              },
              green: {
                border: isSelected ? 'border-green-500 dark:border-green-400' : 'border-gray-300 dark:border-gray-600',
                bg: isSelected ? 'bg-green-50 dark:bg-green-900/20' : 'bg-white dark:bg-gray-800',
                icon: 'text-green-600 dark:text-green-400',
                check: 'text-green-600 dark:text-green-400',
                title: isSelected ? 'text-green-900 dark:text-green-100' : 'text-gray-900 dark:text-white',
                desc: isSelected ? 'text-green-700 dark:text-green-300' : 'text-gray-600 dark:text-gray-400'
              },
              purple: {
                border: isSelected ? 'border-purple-500 dark:border-purple-400' : 'border-gray-300 dark:border-gray-600',
                bg: isSelected ? 'bg-purple-50 dark:bg-purple-900/20' : 'bg-white dark:bg-gray-800',
                icon: 'text-purple-600 dark:text-purple-400',
                check: 'text-purple-600 dark:text-purple-400',
                title: isSelected ? 'text-purple-900 dark:text-purple-100' : 'text-gray-900 dark:text-white',
                desc: isSelected ? 'text-purple-700 dark:text-purple-300' : 'text-gray-600 dark:text-gray-400'
              }
            }[option.color as 'blue' | 'green' | 'purple'] || {
              border: isSelected ? 'border-gray-500 dark:border-gray-400' : 'border-gray-300 dark:border-gray-600',
              bg: isSelected ? 'bg-gray-50 dark:bg-gray-900/20' : 'bg-white dark:bg-gray-800',
              icon: 'text-gray-600 dark:text-gray-400',
              check: 'text-gray-600 dark:text-gray-400',
              title: isSelected ? 'text-gray-900 dark:text-gray-100' : 'text-gray-900 dark:text-white',
              desc: isSelected ? 'text-gray-700 dark:text-gray-300' : 'text-gray-600 dark:text-gray-400'
            };

            return (
              <div
                key={option.value}
                className={`relative rounded-xl p-6 border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${colorClasses.border} ${colorClasses.bg}`}
                onClick={() => updateFormData('product_type', option.value)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`w-10 h-10 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center`}>
                        <IconComponent className={`w-5 h-5 ${colorClasses.icon}`} />
                      </div>
                      <div>
                        <h5 className={`font-semibold ${colorClasses.title}`}>
                          {option.label}
                        </h5>
                      </div>
                    </div>
                    <p className={`text-sm ${colorClasses.desc}`}>
                      {option.description}
                    </p>
                  </div>
                  
                  {isSelected && (
                    <div className={`absolute top-4 left-4 w-6 h-6 rounded-full bg-white dark:bg-gray-700 border-2 ${colorClasses.border} flex items-center justify-center`}>
                      <FiCheck className={`w-3 h-3 ${colorClasses.check}`} />
                    </div>
                  )}
                </div>

                <input
                  type="radio"
                  name="product_type"
                  value={option.value}
                  checked={isSelected}
                  onChange={() => updateFormData('product_type', option.value)}
                  className="sr-only"
                />
              </div>
            );
          })}
        </div>

        {errors.product_type && (
          <p className="text-sm text-red-600 dark:text-red-400">
            {errors.product_type}
          </p>
        )}
      </div>

      {/* Product Pricing Section - Dynamic based on product type */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          {formData.product_type === 'simple' ? (
            <>
              <FiDollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
              تسعير المنتج
            </>
          ) : (
            <>
              <FiLayers className="w-5 h-5 text-green-600 dark:text-green-400" />
              إدارة متغيرات المنتج
            </>
          )}
        </h4>

        {/* Simple Product Pricing */}
        {formData.product_type === 'simple' && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <NumberInput
                label="السعر"
                name="price"
                value={formData.price.toString()}
                onChange={(value) => updateFormData('price', parseFloat(value) || 0)}
                placeholder="0.00"
                min={0}
                step="0.01"
                required
                error={errors.price}
                currency="د.ل"
              />

              <NumberInput
                label="سعر التكلفة"
                name="cost_price"
                value={formData.cost_price.toString()}
                onChange={(value) => updateFormData('cost_price', parseFloat(value) || 0)}
                placeholder="0.00"
                min={0}
                step="0.01"
                error={errors.cost_price}
                currency="د.ل"
              />
            </div>

            {/* Profit Margin and Total Price - Inside simple products section */}
            {formData.price > 0 && (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-2 mb-2">
                    <FiPercent className="w-4 h-4 text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-green-700 dark:text-green-300">هامش الربح</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {calculateProfitMargin()}%
                  </p>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-2 mb-2">
                    <FiDollarSign className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                      السعر الإجمالي مع الضريبة
                    </span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {calculateTotalPrice()} د.ل
                  </p>
                </div>
              </div>
            )}
          </>
        )}

        {/* Variable Product Variants */}
        {formData.product_type === 'variable' && (
          <ProductVariantsSection
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        )}
      </div>

      {/* Tax Settings */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          إعدادات الضريبة
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ModalSelectInput
            label="نوع الضريبة"
            name="tax_type_id"
            value={formData.tax_type_id?.toString() || ''}
            onChange={(value) => {
              updateFormData('tax_type_id', value ? parseInt(value) : null);
              // Reset tax rate when tax type changes
              updateFormData('tax_rate_id', null);
            }}
            options={taxTypeOptions}
            placeholder={taxTypesLoading ? "جاري التحميل..." : "اختر نوع الضريبة..."}
            disabled={taxTypesLoading || taxTypeOptions.length === 0}
            searchable
            error={errors.tax_type_id}
          />

          <ModalSelectInput
            label="قيمة الضريبة"
            name="tax_rate_id"
            value={formData.tax_rate_id?.toString() || ''}
            onChange={(value) => updateFormData('tax_rate_id', value ? parseInt(value) : null)}
            options={taxRateOptions}
            placeholder={
              !formData.tax_type_id 
                ? "اختر نوع الضريبة أولاً..." 
                : taxRatesLoading 
                  ? "جاري التحميل..." 
                  : taxRateOptions.length === 0
                    ? "لا توجد قيم ضريبية متاحة"
                    : "اختر قيمة الضريبة..."
            }
            searchable
            disabled={!formData.tax_type_id || taxRatesLoading || taxRateOptions.length === 0}
            error={errors.tax_rate_id}
          />
        </div>

        {/* Tax Calculation Display */}
        {formData.tax_rate_id && formData.price > 0 && (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-xl p-4 border border-orange-200 dark:border-orange-800">
              <div className="flex items-center gap-2 mb-2">
                <FiPercent className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-medium text-orange-700 dark:text-orange-300">مبلغ الضريبة</span>
              </div>
              <p className="text-xl font-bold text-orange-600 dark:text-orange-400">
                {calculateTaxAmount()} د.ل
              </p>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-4 border border-purple-200 dark:border-purple-800">
              <div className="flex items-center gap-2 mb-2">
                <FiDollarSign className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                <span className="text-sm font-medium text-purple-700 dark:text-purple-300">الإجمالي النهائي</span>
              </div>
              <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                {calculateTotalPrice()} د.ل
              </p>
            </div>
          </div>
        )}

        {(taxTypesLoading || taxRatesLoading) && (
          <div className="mt-4 text-center text-gray-500 dark:text-gray-400">
            <div className="inline-flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
              جاري تحميل بيانات الضرائب...
            </div>
          </div>
        )}

        {!taxTypesLoading && !taxRatesLoading && taxTypeOptions.length === 0 && (
          <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              لا توجد أنواع ضرائب متاحة. يرجى إضافة أنواع الضرائب من قسم إدارة الفهرس أولاً.
            </p>
          </div>
        )}

        {!taxRatesLoading && formData.tax_type_id && taxRateOptions.length === 0 && (
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              لا توجد قيم ضريبية متاحة لنوع الضريبة المختار. يرجى إضافة قيم ضريبية لهذا النوع.
            </p>
          </div>
        )}
      </div>

      {/* Discount Settings */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          إعدادات الخصم
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ModalSelectInput
            label="نوع الخصم"
            name="discount_type"
            value={formData.discount_type}
            onChange={(value) => updateFormData('discount_type', value)}
            options={discountTypeOptions}
            placeholder="اختر نوع الخصم..."
            error={errors.discount_type}
          />

          <NumberInput
            label="قيمة الخصم"
            name="discount_value"
            value={formData.discount_value.toString()}
            onChange={(value) => updateFormData('discount_value', parseFloat(value) || 0)}
            placeholder="0"
            min={0}
            step={formData.discount_type === 'percentage' ? '1' : '0.01'}
            error={errors.discount_value}
            currency={formData.discount_type === 'percentage' ? '%' : 'ر.س'}
          />
        </div>
      </div>

      {/* Note about inventory management */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center gap-3 mb-2">
          <FiPackage className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            ملاحظة حول إدارة المخزون
          </h4>
        </div>
        <p className="text-sm text-blue-600 dark:text-blue-400">
          إدارة الكميات والمخزون تتم من خلال نظام المستودعات المنفصل. هنا نركز على البيانات الأساسية للمنتج والتسعير فقط.
        </p>
      </div>


    </div>
  );
};

export default PricingInventorySection;
