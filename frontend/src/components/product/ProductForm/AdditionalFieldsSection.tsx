/**
 * قسم الحقول الإضافية للمنتج
 * يحتوي على الضمان، اسم المصنع، تاريخ التصنيع، وتاريخ الانتهاء
 */

import React, { useState, useEffect } from 'react';
import { FaCalendarAlt, FaIndustry, FaClock } from 'react-icons/fa';

// Import components
import { TextInput } from '../../inputs';
import DatePicker from '../../DatePicker';
import WarrantyTypeSelector from '../../WarrantyTypeSelector';

interface AdditionalFieldsSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

const AdditionalFieldsSection: React.FC<AdditionalFieldsSectionProps> = ({
  formData,
  updateFormData,
  errors
}) => {
  // State for warranty type
  const [selectedWarrantyType, setSelectedWarrantyType] = useState<any>(null);

  // Handle warranty type selection
  const handleWarrantyTypeSelect = (warrantyType: any) => {
    setSelectedWarrantyType(warrantyType);
    updateFormData('warranty_type_id', warrantyType?.id || null);
    updateFormData('warranty_type', warrantyType?.name_ar || '');
  };

  // Calculate days until expiry
  const calculateDaysUntilExpiry = () => {
    if (!formData.expiry_date) return null;
    
    const expiryDate = new Date(formData.expiry_date);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  // Calculate product age
  const calculateProductAge = () => {
    if (!formData.manufactured_date) return null;
    
    const manufacturedDate = new Date(formData.manufactured_date);
    const today = new Date();
    const diffTime = today.getTime() - manufacturedDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} يوم`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} شهر`;
    } else {
      const years = Math.floor(diffDays / 365);
      const remainingMonths = Math.floor((diffDays % 365) / 30);
      return remainingMonths > 0 ? `${years} سنة و ${remainingMonths} شهر` : `${years} سنة`;
    }
  };

  const daysUntilExpiry = calculateDaysUntilExpiry();
  const productAge = calculateProductAge();

  return (
    <div className="space-y-6">

      {/* Basic Additional Fields - Two Column Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Warranty Field */}
        <div>
          <WarrantyTypeSelector
            selectedWarrantyType={selectedWarrantyType}
            onWarrantyTypeSelect={handleWarrantyTypeSelect}
            className="w-full"
          />
          {errors.warranty_type && (
            <p className="text-red-600 dark:text-red-400 text-xs mt-1">
              {errors.warranty_type}
            </p>
          )}
        </div>

        {/* Manufacturer Field */}
        <div>
          <TextInput
            label="اسم المصنع"
            name="manufacturer"
            value={formData.manufacturer}
            onChange={(value) => updateFormData('manufacturer', value)}
            placeholder="أدخل اسم الشركة المصنعة..."
            error={errors.manufacturer}
            maxLength={100}
          />
          {formData.manufacturer && (
            <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <FaIndustry className="w-4 h-4" />
                <span className="text-sm">
                  مُصنع بواسطة: {formData.manufacturer}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Dates Section - Two Column Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Manufacturing Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            تاريخ التصنيع
          </label>
          <DatePicker
            value={formData.manufactured_date}
            onChange={(value) => updateFormData('manufactured_date', value)}
            placeholder="اختر تاريخ التصنيع..."
            name="manufactured_date"
          />
          {errors.manufactured_date && (
            <p className="text-red-600 dark:text-red-400 text-xs mt-1">
              {errors.manufactured_date}
            </p>
          )}

          {productAge && (
            <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <FaClock className="w-3 h-3" />
                <span className="text-xs">
                  عمر المنتج: {productAge}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Expiry Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            تاريخ انتهاء الصلاحية
          </label>
          <DatePicker
            value={formData.expiry_date}
            onChange={(value) => updateFormData('expiry_date', value)}
            placeholder="اختر تاريخ انتهاء الصلاحية..."
            name="expiry_date"
          />
          {errors.expiry_date && (
            <p className="text-red-600 dark:text-red-400 text-xs mt-1">
              {errors.expiry_date}
            </p>
          )}

          {daysUntilExpiry !== null && (
            <div className={`mt-2 p-2 rounded-lg ${
              daysUntilExpiry < 0
                ? 'bg-red-50 dark:bg-red-900/20'
                : daysUntilExpiry < 30
                  ? 'bg-yellow-50 dark:bg-yellow-900/20'
                  : 'bg-green-50 dark:bg-green-900/20'
            }`}>
              <div className={`flex items-center gap-2 ${
                daysUntilExpiry < 0
                  ? 'text-red-700 dark:text-red-300'
                  : daysUntilExpiry < 30
                    ? 'text-yellow-700 dark:text-yellow-300'
                    : 'text-green-700 dark:text-green-300'
              }`}>
                <FaCalendarAlt className="w-3 h-3" />
                <span className="text-xs">
                  {daysUntilExpiry < 0
                    ? `منتهي الصلاحية منذ ${Math.abs(daysUntilExpiry)} يوم`
                    : daysUntilExpiry === 0
                      ? 'ينتهي اليوم'
                      : `${daysUntilExpiry} يوم حتى انتهاء الصلاحية`
                  }
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Date Validation Warning */}
      {formData.manufactured_date && formData.expiry_date &&
       new Date(formData.manufactured_date) >= new Date(formData.expiry_date) && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-700">
          <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
            <FaCalendarAlt className="w-4 h-4" />
            <span className="text-sm font-medium">
              تحذير: تاريخ انتهاء الصلاحية يجب أن يكون بعد تاريخ التصنيع
            </span>
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
        <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
          نصائح للحقول الإضافية:
        </h5>
        <ul className="text-xs text-gray-700 dark:text-gray-300 space-y-1">
          <li>• معلومات الضمان تساعد في بناء ثقة العملاء</li>
          <li>• اسم المصنع مفيد للبحث والفلترة</li>
          <li>• تواريخ التصنيع والانتهاء مهمة للمنتجات القابلة للتلف</li>
          <li>• سيتم تنبيهك عند اقتراب انتهاء صلاحية المنتجات</li>
        </ul>
      </div>
    </div>
  );
};

export default AdditionalFieldsSection;
