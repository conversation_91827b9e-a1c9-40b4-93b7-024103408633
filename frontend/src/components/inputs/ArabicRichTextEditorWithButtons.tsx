import React, { useEffect, useRef, useState, useCallback } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import '../../styles/arabic-rich-text-editor-fixes.css';
import {
  FiType,
  FiBold,
  FiItalic,
  FiUnderline,
  FiMinus,
  FiList,
  FiMoreHorizontal,
  FiChevronRight,
  FiChevronLeft,
  FiLink,
  FiX
} from 'react-icons/fi';
import { SimpleTooltip } from '../ui';

// تخصيص Quill للعربية
const DirectionAttribute = Quill.import('attributors/attribute/direction');
const AlignStyle = Quill.import('attributors/style/align');
const BackgroundStyle = Quill.import('attributors/style/background');
const ColorStyle = Quill.import('attributors/style/color');
const FontStyle = Quill.import('attributors/style/font');
const SizeStyle = Quill.import('attributors/style/size');

Quill.register(DirectionAttribute, true);
Quill.register(AlignStyle, true);
Quill.register(BackgroundStyle, true);
Quill.register(ColorStyle, true);
Quill.register(FontStyle, true);
Quill.register(SizeStyle, true);

interface ArabicRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  minHeight?: string;
}

// أنماط CSS محسنة للعربية مع أزرار العناوين - متوافقة مع النظام الموحد
const arabicEditorStyles = `
  .arabic-quill-editor-with-buttons {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    border-radius: 1rem; /* rounded-xl */
    overflow: hidden;
    transition: all 0.2s ease-in-out;
    border: 2px solid rgba(209, 213, 219, 0.6); /* border-gray-300/60 - نفس لون المكونات الأخرى */
  }

  .arabic-quill-editor-with-buttons .custom-toolbar {
    direction: rtl !important;
    border-top-right-radius: 1rem; /* rounded-xl */
    border-top-left-radius: 1rem; /* rounded-xl */
    border: none; /* إزالة الحد لأن الحاوية الرئيسية لها حد */
    border-bottom: 1px solid rgba(209, 213, 219, 0.3); /* فاصل أخف بين الشريط والمحرر */
    background: #ffffff; /* خلفية بيضاء نظيفة */
    padding: 8px 12px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
    overflow-x: auto;
    overflow-y: hidden;
    min-height: 44px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03); /* ظل أخف جداً */
    position: relative;
    z-index: 10;
    transition: all 0.2s ease-in-out;
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-group {
    display: flex;
    align-items: center;
    gap: 1px;
    margin: 0;
    flex-shrink: 0;
    padding: 2px;
    background: #ffffff;
    border-radius: 0.5rem; /* rounded-lg */
    border: 1px solid rgba(209, 213, 219, 0.4); /* حد أخف */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03); /* ظل أخف جداً */
    transition: all 0.2s ease-in-out;
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none; /* إزالة الحدود للحصول على مظهر أنظف */
    border-radius: 0.375rem; /* rounded-md */
    background: transparent;
    margin: 0;
    transition: all 0.15s ease-in-out; /* انتقال أسرع */
    font-size: 12px;
    color: #6b7280; /* لون أخف */
    cursor: pointer;
    user-select: none;
    position: relative;
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button:hover {
    background: rgba(59, 130, 246, 0.08); /* خلفية زرقاء خفيفة جداً */
    color: #374151;
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button.active {
    background: rgba(59, 130, 246, 0.12); /* خلفية زرقاء أكثر وضوحاً للحالة النشطة */
    color: #1d4ed8; /* لون أزرق داكن للنص */
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button:disabled:hover {
    background: transparent;
    color: #6b7280;
  }

  .arabic-quill-editor-with-buttons .custom-toolbar .header-size-display {
    font-size: 10px;
    color: #6b7280;
    margin: 0 6px;
    min-width: 70px;
    text-align: center;
    background: rgba(243, 244, 246, 0.6); /* خلفية أخف */
    padding: 6px 10px;
    border-radius: 0.375rem; /* rounded-md */
    border: none; /* إزالة الحد */
    transition: all 0.2s ease-in-out;
    font-weight: 500;
  }

  /* إخفاء شريط الأدوات الافتراضي تماماً */
  .arabic-quill-editor-with-buttons .ql-toolbar {
    display: none !important;
  }

  .arabic-quill-editor-with-buttons .ql-container {
    direction: rtl !important;
    border-bottom-right-radius: 1rem !important; /* rounded-xl */
    border-bottom-left-radius: 1rem !important; /* rounded-xl */
    border: none !important; /* إزالة الحد لأن الحاوية الرئيسية لها حد */
    border-top: none !important;
    overflow: visible !important;
    background: #ffffff !important;
    box-shadow: none !important; /* إزالة الظل لأن الحاوية الرئيسية لها ظل */
    margin-top: 0 !important;
    transition: all 0.2s ease-in-out !important;
  }

  /* تأثير التركيز على الحاوية الرئيسية */
  .arabic-quill-editor-with-buttons:focus-within {
    border-color: #0ea5e9 !important; /* focus:border-primary-500 */
    box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.2) !important; /* focus:ring-4 focus:ring-primary-500/20 */
  }

  .arabic-quill-editor-with-buttons:focus-within .custom-toolbar {
    border-bottom-color: rgba(14, 165, 233, 0.3) !important; /* فاصل أزرق خفيف عند التركيز */
  }

  .arabic-quill-editor-with-buttons .ql-editor {
    direction: rtl !important;
    text-align: right !important;
    min-height: 180px;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    line-height: 1.6;
    font-size: 14px;
    padding: 12px 25px 12px 15px;
    unicode-bidi: embed;
    overflow: visible !important;
    transition: all 0.2s ease-in-out !important;
  }

  .arabic-quill-editor-with-buttons .ql-editor.ql-blank::before {
    direction: rtl !important;
    text-align: right !important;
    font-style: italic;
    color: #9ca3af; /* placeholder:text-gray-400 */
    left: auto !important;
    right: 25px !important;
  }

  /* إصلاح القوائم للعربية */
  .arabic-quill-editor-with-buttons .ql-editor ol,
  .arabic-quill-editor-with-buttons .ql-editor ul {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0.5em 0;
    margin-right: 2em !important;
    position: relative;
    overflow: visible !important;
  }

  .arabic-quill-editor-with-buttons .ql-editor ol li,
  .arabic-quill-editor-with-buttons .ql-editor ul li {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0.25em 0;
    list-style: none !important;
    position: relative;
    overflow: visible !important;
  }

  /* النقاط والأرقام */
  .arabic-quill-editor-with-buttons .ql-editor ul li::before {
    content: "•" !important;
    position: absolute !important;
    right: -1.8em !important;
    top: 0 !important;
    color: #374151 !important;
    font-weight: bold !important;
    font-size: 1.2em !important;
    line-height: 1.4 !important;
    z-index: 1 !important;
  }

  .arabic-quill-editor-with-buttons .ql-editor ol {
    counter-reset: arabic-counter !important;
  }

  .arabic-quill-editor-with-buttons .ql-editor ol li {
    counter-increment: arabic-counter !important;
  }

  .arabic-quill-editor-with-buttons .ql-editor ol li::before {
    content: counter(arabic-counter) "." !important;
    position: absolute !important;
    right: -2.2em !important;
    top: 0 !important;
    color: #374151 !important;
    font-weight: bold !important;
    line-height: 1.4 !important;
    z-index: 1 !important;
    min-width: 1.5em !important;
    text-align: left !important;
  }

  /* الوضع المظلم - متوافق مع النظام الموحد */
  .dark .arabic-quill-editor-with-buttons {
    border-color: rgba(75, 85, 99, 0.4) !important; /* dark:border-gray-600/40 - نفس لون المكونات الأخرى */
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar {
    background: #1f2937 !important; /* خلفية داكنة نظيفة */
    border-bottom-color: rgba(75, 85, 99, 0.2) !important; /* فاصل أخف للوضع المظلم */
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-group {
    background: #374151 !important; /* خلفية داكنة متناسقة مع الهيدر */
    border-color: rgba(75, 85, 99, 0.25) !important; /* حد أخف */
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button {
    background: transparent !important;
    color: #d1d5db !important; /* لون فاتح للأيقونات */
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button:hover {
    background: rgba(59, 130, 246, 0.15) !important; /* خلفية زرقاء خفيفة للوضع المظلم */
    color: #e5e7eb !important;
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button.active {
    background: rgba(59, 130, 246, 0.2) !important; /* خلفية زرقاء أكثر وضوحاً */
    color: #60a5fa !important; /* لون أزرق فاتح */
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button:disabled:hover {
    background: transparent !important;
    color: #6b7280 !important;
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .header-size-display {
    background: rgba(75, 85, 99, 0.4) !important; /* خلفية داكنة خفيفة */
    color: #d1d5db !important;
  }

  /* إخفاء شريط الأدوات الافتراضي في الوضع المظلم أيضاً */
  .dark .arabic-quill-editor-with-buttons .ql-toolbar {
    display: none !important;
  }

  .dark .arabic-quill-editor-with-buttons .ql-container {
    background: #1f2937 !important; /* dark:bg-gray-800 */
  }

  /* تأثير التركيز في الوضع المظلم */
  .dark .arabic-quill-editor-with-buttons:focus-within {
    border-color: #0ea5e9 !important; /* focus:border-primary-500 */
    box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.2) !important; /* focus:ring-4 focus:ring-primary-500/20 */
  }

  .dark .arabic-quill-editor-with-buttons:focus-within .custom-toolbar {
    border-bottom-color: rgba(14, 165, 233, 0.4) !important; /* فاصل أزرق أوضح قليلاً للوضع المظلم */
  }

  .dark .arabic-quill-editor-with-buttons .ql-editor {
    background: #1f2937 !important; /* dark:bg-gray-800 */
    color: #f9fafb !important; /* dark:text-gray-100 */
  }

  .dark .arabic-quill-editor-with-buttons .ql-editor.ql-blank::before {
    color: #6b7280 !important; /* dark:placeholder:text-gray-500 */
  }

  .dark .arabic-quill-editor-with-buttons .ql-editor ul li::before,
  .dark .arabic-quill-editor-with-buttons .ql-editor ol li::before {
    color: #d1d5db !important; /* dark:text-gray-300 */
  }

  /* التصميم المتجاوب - متوافق مع النظام الموحد */
  @media (max-width: 768px) {
    .arabic-quill-editor-with-buttons .custom-toolbar {
      padding: 6px !important;
      gap: 2px !important;
      border-radius: 0.75rem !important; /* rounded-xl للشاشات الصغيرة */
    }

    .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-button {
      width: 30px !important;
      height: 30px !important;
      font-size: 11px !important;
      border-radius: 0.25rem !important; /* rounded للشاشات الصغيرة */
    }

    .arabic-quill-editor-with-buttons .custom-toolbar .header-size-display {
      min-width: 60px !important;
      font-size: 9px !important;
      padding: 4px 8px !important;
      border-radius: 0.25rem !important; /* rounded للشاشات الصغيرة */
    }

    .arabic-quill-editor-with-buttons .ql-editor {
      padding: 10px 20px 10px 12px !important;
      font-size: 16px !important; /* منع التكبير التلقائي في iOS */
    }

    .arabic-quill-editor-with-buttons .ql-container {
      border-radius: 0 0 0.75rem 0.75rem !important; /* rounded-xl للشاشات الصغيرة */
    }
  }

  /* تحسينات إضافية للتفاعل - بدون حركة */
  .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-group:hover {
    border-color: rgba(209, 213, 219, 0.6); /* حد أوضح قليلاً عند hover */
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.08); /* ظل خفيف */
  }

  .dark .arabic-quill-editor-with-buttons .custom-toolbar .toolbar-group:hover {
    border-color: rgba(75, 85, 99, 0.5); /* حد أوضح للوضع المظلم */
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2); /* ظل للوضع المظلم */
  }
`;

const ArabicRichTextEditorWithButtons: React.FC<ArabicRichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'اكتب وصف المنتج هنا...',
  className = '',
  disabled = false,
  minHeight = '180px'
}) => {
  const editorRef = useRef<ReactQuill>(null);
  const [isReady, setIsReady] = useState(false);
  const [currentHeaderLevel, setCurrentHeaderLevel] = useState<number>(0); // 0 = normal, 1-3 = header levels
  const [activeFormats, setActiveFormats] = useState<{[key: string]: boolean}>({});

  // إعدادات المحرر بدون شريط أدوات (سنستخدم الشريط المخصص)
  const modules = {
    toolbar: false, // إخفاء شريط الأدوات الافتراضي
    clipboard: {
      matchVisual: false
    }
  };

  const formats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent', 'link', 'clean',
    'align', 'script'
  ];

  // تطبيق تحسينات العربية
  const applyArabicEnhancements = useCallback(() => {
    if (!editorRef.current) return;

    const container = editorRef.current.getEditingArea();
    
    if (container) {
      // تطبيق الاتجاه العربي
      container.setAttribute('dir', 'rtl');
      const editorElement = container.querySelector('.ql-editor');
      if (editorElement) {
        (editorElement as HTMLElement).style.direction = 'rtl';
        (editorElement as HTMLElement).style.textAlign = 'right';
        (editorElement as HTMLElement).style.minHeight = minHeight;
      }
    }

    // تحديث مستوى العنوان الحالي
    updateCurrentHeaderLevel();
  }, [minHeight]);

  // تحديث مستوى العنوان الحالي وحالة الأزرار النشطة
  const updateCurrentHeaderLevel = useCallback(() => {
    if (!editorRef.current) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (range) {
      const format = editor.getFormat(range);
      const headerLevel = format.header || 0;
      setCurrentHeaderLevel(headerLevel);
      
      // تحديث حالة الأزرار النشطة
      setActiveFormats({
        bold: !!format.bold,
        italic: !!format.italic,
        underline: !!format.underline,
        strike: !!format.strike,
        list: !!format.list,
        indent: (format.indent || 0) > 0
      });
    }
  }, []);

  // زيادة حجم العنوان
  const increaseHeaderSize = useCallback(() => {
    if (!editorRef.current || disabled) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (range) {
      let newLevel = currentHeaderLevel;
      if (newLevel === 0) {
        newLevel = 3; // من نص عادي إلى عنوان صغير
      } else if (newLevel === 3) {
        newLevel = 2; // من عنوان صغير إلى متوسط
      } else if (newLevel === 2) {
        newLevel = 1; // من عنوان متوسط إلى كبير
      }
      // إذا كان 1 (كبير) يبقى كما هو
      
      editor.format('header', newLevel === 0 ? false : newLevel);
      setCurrentHeaderLevel(newLevel);
    }
  }, [currentHeaderLevel, disabled]);

  // تقليل حجم العنوان
  const decreaseHeaderSize = useCallback(() => {
    if (!editorRef.current || disabled) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (range) {
      let newLevel = currentHeaderLevel;
      if (newLevel === 1) {
        newLevel = 2; // من عنوان كبير إلى متوسط
      } else if (newLevel === 2) {
        newLevel = 3; // من عنوان متوسط إلى صغير
      } else if (newLevel === 3) {
        newLevel = 0; // من عنوان صغير إلى نص عادي
      }
      // إذا كان 0 (نص عادي) يبقى كما هو
      
      editor.format('header', newLevel === 0 ? false : newLevel);
      setCurrentHeaderLevel(newLevel);
    }
  }, [currentHeaderLevel, disabled]);

  // تطبيق تنسيق
  const applyFormat = useCallback((format: string, value?: any) => {
    if (!editorRef.current || disabled) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (!range) {
      // إذا لم يكن هناك تحديد، ضع المؤشر في نهاية النص
      const length = editor.getLength();
      editor.setSelection(length - 1, 0);
      return;
    }

    try {
      if (format === 'clean') {
        // إزالة جميع التنسيقات
        editor.removeFormat(range.index, range.length);
      } else if (format === 'link') {
        // التعامل مع الروابط
        if (value) {
          editor.format('link', value);
        } else {
          editor.format('link', false);
        }
      } else if (format === 'indent') {
        // التعامل مع المسافات البادئة
        const currentFormat = editor.getFormat(range);
        const currentIndent = currentFormat.indent || 0;
        
        if (value === '+1') {
          editor.format('indent', currentIndent + 1);
        } else if (value === '-1' && currentIndent > 0) {
          editor.format('indent', currentIndent - 1);
        }
      } else {
        // التنسيقات العادية - التحقق من الحالة الحالية للتبديل
        const currentFormat = editor.getFormat(range);
        const isActive = currentFormat[format];
        
        if (format === 'bold' || format === 'italic' || format === 'underline' || format === 'strike') {
          // تبديل التنسيق
          editor.format(format, !isActive);
        } else {
          // تطبيق التنسيق مباشرة
          editor.format(format, value);
        }
      }
      
      // تحديث مستوى العنوان إذا كان التنسيق متعلق بالعناوين
      if (format === 'header') {
        setTimeout(updateCurrentHeaderLevel, 10);
      }
      
      // إعادة التركيز على المحرر
      editor.focus();
    } catch (error) {
      console.error('خطأ في تطبيق التنسيق:', error);
    }
  }, [disabled, updateCurrentHeaderLevel]);

  // الحصول على نص مستوى العنوان
  const getHeaderLevelText = (level: number): string => {
    switch (level) {
      case 1: return 'عنوان كبير';
      case 2: return 'عنوان متوسط';
      case 3: return 'عنوان صغير';
      default: return 'نص عادي';
    }
  };

  // معالج تغيير النص
  const handleChange = useCallback((content: string) => {
    onChange(content);
    // تحديث مستوى العنوان بعد التغيير
    setTimeout(updateCurrentHeaderLevel, 10);
  }, [onChange, updateCurrentHeaderLevel]);

  useEffect(() => {
    setIsReady(true);
  }, []);

  useEffect(() => {
    if (isReady) {
      const timer = setTimeout(applyArabicEnhancements, 50);
      return () => clearTimeout(timer);
    }
  }, [value, isReady, applyArabicEnhancements]);

  // مراقبة تغييرات التحديد لتحديث مستوى العنوان
  useEffect(() => {
    if (!editorRef.current || !isReady) return;

    const editor = editorRef.current.getEditor();
    
    const handleSelectionChange = () => {
      updateCurrentHeaderLevel();
    };

    const handleTextChange = () => {
      // تحديث حالة الأزرار عند تغيير النص
      setTimeout(updateCurrentHeaderLevel, 10);
    };

    // إضافة مستمعين للأحداث
    editor.on('selection-change', handleSelectionChange);
    editor.on('text-change', handleTextChange);

    // تنظيف المستمعين عند إلغاء التحميل
    return () => {
      editor.off('selection-change', handleSelectionChange);
      editor.off('text-change', handleTextChange);
    };
  }, [isReady, updateCurrentHeaderLevel]);

  // دعم اختصارات لوحة المفاتيح
  useEffect(() => {
    if (!editorRef.current || !isReady) return;

    const handleKeyDown = (e: Event) => {
      const keyboardEvent = e as KeyboardEvent;
      if (keyboardEvent.ctrlKey || keyboardEvent.metaKey) {
        switch (keyboardEvent.key.toLowerCase()) {
          case 'b':
            keyboardEvent.preventDefault();
            applyFormat('bold');
            break;
          case 'i':
            keyboardEvent.preventDefault();
            applyFormat('italic');
            break;
          case 'u':
            keyboardEvent.preventDefault();
            applyFormat('underline');
            break;
        }
      }
    };

    const editorElement = editorRef.current.getEditingArea();
    if (editorElement) {
      editorElement.addEventListener('keydown', handleKeyDown);
      return () => {
        editorElement.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isReady, applyFormat]);

  return (
    <div className={`arabic-quill-editor-with-buttons ${className}`} dir="rtl">
      <style>{arabicEditorStyles}</style>
      
      {/* شريط الأدوات المخصص */}
      <div className="custom-toolbar">
        {/* مجموعة أزرار العناوين */}
        <div className="toolbar-group">
          <SimpleTooltip text="زيادة حجم العنوان" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={increaseHeaderSize}
              disabled={disabled || currentHeaderLevel === 1}
            >
              <FiType className="text-blue-600 dark:text-blue-400" size={14} />
            </button>
          </SimpleTooltip>
          <div className="header-size-display">
            {getHeaderLevelText(currentHeaderLevel)}
          </div>
          <SimpleTooltip text="تقليل حجم العنوان" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={decreaseHeaderSize}
              disabled={disabled || currentHeaderLevel === 0}
            >
              <FiMinus className="text-blue-600 dark:text-blue-400" size={14} />
            </button>
          </SimpleTooltip>
        </div>

        {/* مجموعة تنسيق النص */}
        <div className="toolbar-group">
          <SimpleTooltip text="عريض (Ctrl+B)" position="top">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.bold ? 'active' : ''}`}
              onClick={() => applyFormat('bold')}
              disabled={disabled}
            >
              <FiBold className="text-gray-700 dark:text-gray-300" size={14} />
            </button>
          </SimpleTooltip>
          <SimpleTooltip text="مائل (Ctrl+I)" position="top">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.italic ? 'active' : ''}`}
              onClick={() => applyFormat('italic')}
              disabled={disabled}
            >
              <FiItalic className="text-gray-700 dark:text-gray-300" size={14} />
            </button>
          </SimpleTooltip>
          <SimpleTooltip text="تحته خط (Ctrl+U)" position="top">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.underline ? 'active' : ''}`}
              onClick={() => applyFormat('underline')}
              disabled={disabled}
            >
              <FiUnderline className="text-gray-700 dark:text-gray-300" size={14} />
            </button>
          </SimpleTooltip>
          <SimpleTooltip text="يتوسطه خط" position="top">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.strike ? 'active' : ''}`}
              onClick={() => applyFormat('strike')}
              disabled={disabled}
            >
              <FiMinus className="text-gray-700 dark:text-gray-300" size={14} />
            </button>
          </SimpleTooltip>
        </div>

        {/* مجموعة القوائم */}
        <div className="toolbar-group">
          <SimpleTooltip text="قائمة مرقمة" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('list', 'ordered')}
              disabled={disabled}
            >
              <FiList className="text-green-600 dark:text-green-400" size={14} />
            </button>
          </SimpleTooltip>
          <SimpleTooltip text="قائمة نقطية" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('list', 'bullet')}
              disabled={disabled}
            >
              <FiMoreHorizontal className="text-green-600 dark:text-green-400" size={14} />
            </button>
          </SimpleTooltip>
        </div>

        {/* مجموعة المسافات البادئة */}
        <div className="toolbar-group">
          <SimpleTooltip text="تقليل المسافة البادئة" position="top">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.indent ? 'active' : ''}`}
              onClick={() => applyFormat('indent', '-1')}
              disabled={disabled || !activeFormats.indent}
            >
              <FiChevronRight className="text-orange-600 dark:text-orange-400" size={14} />
            </button>
          </SimpleTooltip>
          <SimpleTooltip text="زيادة المسافة البادئة" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('indent', '+1')}
              disabled={disabled}
            >
              <FiChevronLeft className="text-orange-600 dark:text-orange-400" size={14} />
            </button>
          </SimpleTooltip>
        </div>

        {/* مجموعة الروابط والتنظيف */}
        <div className="toolbar-group">
          <SimpleTooltip text="إضافة رابط" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => {
                const url = prompt('أدخل الرابط:');
                if (url) {
                  applyFormat('link', url);
                }
              }}
              disabled={disabled}
            >
              <FiLink className="text-purple-600 dark:text-purple-400" size={14} />
            </button>
          </SimpleTooltip>
          <SimpleTooltip text="إزالة التنسيق" position="top">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('clean')}
              disabled={disabled}
            >
              <FiX className="text-red-600 dark:text-red-400" size={14} />
            </button>
          </SimpleTooltip>
        </div>
      </div>

      {/* محرر النصوص */}
      <ReactQuill
        ref={editorRef}
        theme="snow"
        value={value}
        onChange={handleChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        readOnly={disabled}
      />
    </div>
  );
};

export default ArabicRichTextEditorWithButtons;