import React, { useState, forwardRef } from 'react';
import { FaBarcode, FaSync, FaExclamationTriangle, FaCheckCircle, FaSpinner } from 'react-icons/fa';
import { SimpleTooltip } from '../ui';

interface BarcodeInputProps {
  label?: string;
  name: string;
  id?: string;
  value: string;
  onChange: (value: string) => void;
  onGenerate?: () => string;
  onValidate?: (barcode: string) => Promise<boolean>;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  success?: string;
  className?: string;
  autoGenerate?: boolean;
  showGenerateButton?: boolean;
  validating?: boolean;
}

const BarcodeInput = forwardRef<HTMLInputElement, BarcodeInputProps>(({
  label,
  name,
  value,
  onChange,
  onGenerate,
  onValidate,
  placeholder = 'أدخل الباركود أو اضغط توليد',
  required = false,
  disabled = false,
  error,
  success,
  className = '',

  showGenerateButton = true,
  validating = false
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    // Validate barcode if validation function is provided
    if (onValidate && newValue.trim()) {
      onValidate(newValue);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasInteracted(true);
  };

  const handleGenerate = () => {
    if (onGenerate) {
      const newBarcode = onGenerate();
      onChange(newBarcode);
      setHasInteracted(true);
    }
  };

  const showError = error && hasInteracted;
  const showSuccess = success && hasInteracted && !error && !validating;
  const isEmpty = !value || value.trim() === '';
  const showRequiredMessage = required && isEmpty && hasInteracted && !error;

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}

      {/* Input Container */}
      <div className="relative flex gap-2">
        {/* Input Field Container */}
        <div className="relative flex-1">
          {/* Barcode Icon */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
            <div className={`transition-colors duration-200 ${
              validating
                ? 'text-blue-500'
                : showError 
                  ? 'text-red-500' 
                  : showSuccess 
                    ? 'text-green-500'
                    : isFocused 
                      ? 'text-primary-500' 
                      : 'text-gray-400 dark:text-gray-500'
            }`}>
              {validating ? (
                <FaSpinner className="animate-spin" />
              ) : (
                <FaBarcode />
              )}
            </div>
          </div>

          {/* Input Field */}
          <input
            ref={ref}
            type="text"
            name={name}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            dir="ltr"
            className={`
              w-full h-10 rounded-xl border-2 px-4 pr-12 transition-all duration-200 ease-in-out
              ${disabled 
                ? 'bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700' 
                : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
              }
              ${showError
                ? 'border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20'
                : showSuccess
                  ? 'border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20'
                  : validating
                    ? 'border-blue-500 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20'
                    : isFocused
                      ? 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20'
                      : 'border-gray-300/60 dark:border-gray-600/40'
              }
              focus:outline-none
              placeholder:text-gray-400 dark:placeholder:text-gray-500
              font-mono
            `}
          />

          {/* Status Icon */}
          {(showError || showSuccess) && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              {showError ? (
                <FaExclamationTriangle className="h-5 w-5 text-red-500" />
              ) : (
                <FaCheckCircle className="h-5 w-5 text-green-500" />
              )}
            </div>
          )}
        </div>

        {/* Generate Button */}
        {showGenerateButton && (
          <SimpleTooltip text="توليد باركود جديد" position="top">
            <button
              type="button"
              onClick={handleGenerate}
              disabled={disabled}
              className={`
                h-10 px-3 rounded-xl border-2 transition-all duration-200 ease-in-out
                flex items-center justify-center
                ${disabled
                  ? 'bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-600 border-gray-200/60 dark:border-gray-700/40 cursor-not-allowed'
                  : 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/40 hover:border-primary-300 dark:hover:border-primary-700'
                }
                focus:outline-none focus:ring-4 focus:ring-primary-500/20
              `}
            >
              <FaSync className="h-4 w-4" />
            </button>
          </SimpleTooltip>
        )}
      </div>

      {/* Validation Status */}
      {validating && (
        <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <p className="text-blue-700 dark:text-blue-300 text-sm flex items-center">
            <FaSpinner className="animate-spin ml-2 flex-shrink-0 text-blue-500" />
            جاري التحقق من الباركود...
          </p>
        </div>
      )}

      {/* Error Message */}
      {showError && (
        <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
            {error}
          </p>
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-700 dark:text-green-300 text-sm flex items-center">
            <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
            {success}
          </p>
        </div>
      )}

      {/* Required Message */}
      {showRequiredMessage && (
        <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <p className="text-orange-700 dark:text-orange-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-orange-500" />
            هذا الحقل مطلوب، يرجى إدخال الباركود أو توليد باركود جديد
          </p>
        </div>
      )}


    </div>
  );
});

BarcodeInput.displayName = 'BarcodeInput';

export default BarcodeInput;
